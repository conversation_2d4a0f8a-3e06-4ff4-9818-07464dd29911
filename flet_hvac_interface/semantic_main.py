"""
🚀 Enhanced Flet Interface with Semantic Integration
Simplified main application with modular semantic services
"""

import flet as ft
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# Import modular semantic services
from semantic_services import (
    SemanticFrameworkManager,
    HVACAgentManager,
    AgentType,
    TaskPriority,
    CustomerIntelligenceService,
    EmailProcessorService
)

logger = logging.getLogger(__name__)

class SemanticHVACApp:
    """
    🚀 Main HVAC CRM Application with Semantic Intelligence
    """
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.setup_page()
        
        # Initialize semantic services
        self.semantic_manager = SemanticFrameworkManager()
        self.agent_manager = HVACAgentManager()
        self.customer_service = CustomerIntelligenceService()
        self.email_service = EmailProcessorService()
        
        # UI components
        self.navigation_rail = None
        self.content_area = None
        self.status_bar = None
        
        logger.info("🚀 Semantic HVAC App initialized")
    
    def setup_page(self):
        """Setup page configuration"""
        self.page.title = "Fulmark HVAC CRM - Semantic Intelligence"
        self.page.theme_mode = ft.ThemeMode.DARK
        self.page.window_width = 1400
        self.page.window_height = 900
        self.page.padding = 0
        self.page.spacing = 0
    
    def build_app(self):
        """Build the main application"""
        # Create navigation rail
        self.navigation_rail = ft.NavigationRail(
            selected_index=0,
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=100,
            min_extended_width=200,
            destinations=[
                ft.NavigationRailDestination(
                    icon=ft.icons.DASHBOARD,
                    selected_icon=ft.icons.DASHBOARD_OUTLINED,
                    label="Dashboard"
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.EMAIL,
                    selected_icon=ft.icons.EMAIL_OUTLINED,
                    label="Email Intel"
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.PEOPLE,
                    selected_icon=ft.icons.PEOPLE_OUTLINED,
                    label="Customers"
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.SMART_TOY,
                    selected_icon=ft.icons.SMART_TOY_OUTLINED,
                    label="AI Agents"
                ),
                ft.NavigationRailDestination(
                    icon=ft.icons.ANALYTICS,
                    selected_icon=ft.icons.ANALYTICS_OUTLINED,
                    label="Analytics"
                )
            ],
            on_change=self.on_navigation_change
        )
        
        # Create content area
        self.content_area = ft.Container(
            content=self.build_dashboard(),
            expand=True,
            padding=20
        )
        
        # Create status bar
        self.status_bar = ft.Container(
            content=ft.Row([
                ft.Text("🧠 Semantic Framework: Active", size=12),
                ft.Text("🤖 Agents: Ready", size=12),
                ft.Text(f"⏰ {datetime.now().strftime('%H:%M:%S')}", size=12)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            height=30,
            padding=ft.padding.symmetric(horizontal=20, vertical=5),
            bgcolor=ft.colors.SURFACE_VARIANT
        )
        
        # Main layout
        main_layout = ft.Row([
            self.navigation_rail,
            ft.VerticalDivider(width=1),
            self.content_area
        ], expand=True)
        
        # Add to page
        self.page.add(
            ft.Column([
                main_layout,
                self.status_bar
            ], expand=True, spacing=0)
        )
    
    def on_navigation_change(self, e):
        """Handle navigation changes"""
        selected_index = e.control.selected_index
        
        if selected_index == 0:
            self.content_area.content = self.build_dashboard()
        elif selected_index == 1:
            self.content_area.content = self.build_email_intel()
        elif selected_index == 2:
            self.content_area.content = self.build_customers()
        elif selected_index == 3:
            self.content_area.content = self.build_ai_agents()
        elif selected_index == 4:
            self.content_area.content = self.build_analytics()
        
        self.page.update()
    
    def build_dashboard(self):
        """Build semantic dashboard"""
        return ft.Column([
            ft.Text("🧠 Semantic Intelligence Dashboard", 
                   size=24, weight=ft.FontWeight.BOLD),
            
            ft.Row([
                self.build_metric_card("Total Analyses", "1,247", ft.icons.ANALYTICS),
                self.build_metric_card("Active Agents", "5", ft.icons.SMART_TOY),
                self.build_metric_card("Customer Insights", "89", ft.icons.LIGHTBULB),
                self.build_metric_card("Avg Response Time", "0.3s", ft.icons.SPEED)
            ]),
            
            ft.Row([
                ft.Container(
                    content=ft.Column([
                        ft.Text("🔍 Recent Semantic Analysis", weight=ft.FontWeight.BOLD),
                        ft.Divider(),
                        ft.Text("• Customer email: Positive sentiment (0.8)"),
                        ft.Text("• Service request: High urgency (0.9)"),
                        ft.Text("• Maintenance inquiry: Medium priority (0.5)")
                    ]),
                    bgcolor=ft.colors.SURFACE_VARIANT,
                    padding=20,
                    border_radius=10,
                    expand=True
                ),
                ft.Container(
                    content=ft.Column([
                        ft.Text("🤖 Agent Activity", weight=ft.FontWeight.BOLD),
                        ft.Divider(),
                        ft.Text("• Conversational: 3 tasks completed"),
                        ft.Text("• Analytical: 2 analyses running"),
                        ft.Text("• Integration: Data sync in progress")
                    ]),
                    bgcolor=ft.colors.SURFACE_VARIANT,
                    padding=20,
                    border_radius=10,
                    expand=True
                )
            ])
        ])
    
    def build_email_intel(self):
        """Build email intelligence view"""
        return ft.Column([
            ft.Text("📧 Email Intelligence", size=24, weight=ft.FontWeight.BOLD),
            
            ft.Row([
                ft.ElevatedButton(
                    "🔍 Analyze Email",
                    on_click=self.analyze_sample_email
                ),
                ft.ElevatedButton(
                    "📬 Check New Emails",
                    on_click=self.check_emails
                )
            ]),
            
            ft.Container(
                content=ft.Column([
                    ft.Text("Recent Email Analysis", weight=ft.FontWeight.BOLD),
                    ft.Divider(),
                    ft.Text("No recent analyses. Click 'Analyze Email' to start.")
                ], scroll=ft.ScrollMode.AUTO),
                bgcolor=ft.colors.SURFACE_VARIANT,
                padding=20,
                border_radius=10,
                height=400
            )
        ])
    
    def build_customers(self):
        """Build customer intelligence view"""
        return ft.Column([
            ft.Text("👥 Customer Intelligence", size=24, weight=ft.FontWeight.BOLD),
            
            ft.TextField(
                label="Search customers...",
                prefix_icon=ft.icons.SEARCH,
                on_submit=self.search_customers
            ),
            
            ft.Container(
                content=ft.Text("Customer profiles will appear here"),
                bgcolor=ft.colors.SURFACE_VARIANT,
                padding=20,
                border_radius=10,
                height=400
            )
        ])
    
    def build_ai_agents(self):
        """Build AI agents management view"""
        return ft.Column([
            ft.Text("🤖 AI Agents Management", size=24, weight=ft.FontWeight.BOLD),
            
            ft.Row([
                ft.ElevatedButton(
                    "🗣️ Test Conversational Agent",
                    on_click=self.test_conversational_agent
                ),
                ft.ElevatedButton(
                    "📊 View Agent Status",
                    on_click=self.show_agent_status
                )
            ]),
            
            ft.Container(
                content=ft.Column([
                    ft.Text("Agent Status", weight=ft.FontWeight.BOLD),
                    ft.Divider(),
                    ft.Text("🗣️ Conversational Agent: Ready"),
                    ft.Text("📊 Analytical Agent: Not implemented"),
                    ft.Text("🎯 Decision Agent: Not implemented"),
                    ft.Text("🔗 Integration Agent: Not implemented"),
                    ft.Text("⚡ Optimization Agent: Not implemented")
                ]),
                bgcolor=ft.colors.SURFACE_VARIANT,
                padding=20,
                border_radius=10,
                height=300
            )
        ])
    
    def build_analytics(self):
        """Build analytics view"""
        return ft.Column([
            ft.Text("📊 Semantic Analytics", size=24, weight=ft.FontWeight.BOLD),
            
            ft.Container(
                content=ft.Text("Analytics dashboard coming soon..."),
                bgcolor=ft.colors.SURFACE_VARIANT,
                padding=20,
                border_radius=10,
                height=400
            )
        ])
    
    def build_metric_card(self, title: str, value: str, icon):
        """Build metric card"""
        return ft.Container(
            content=ft.Column([
                ft.Icon(icon, size=30),
                ft.Text(value, size=20, weight=ft.FontWeight.BOLD),
                ft.Text(title, size=12)
            ], alignment=ft.MainAxisAlignment.CENTER,
               horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            bgcolor=ft.colors.SURFACE_VARIANT,
            padding=20,
            border_radius=10,
            width=150,
            height=120
        )
    
    async def analyze_sample_email(self, e):
        """Analyze sample email"""
        sample_text = "Dzień dobry, mam pilny problem z klimatyzacją. Przestała działać i jest bardzo gorąco."
        
        try:
            result = await self.semantic_manager.analyze_customer_communication(
                communication_text=sample_text,
                communication_type="email"
            )
            
            # Update email intel view with results
            analysis_text = f"""
📧 Sample Email Analysis:
• Sentiment: {result.sentiment.get('label', 'unknown')} ({result.sentiment.get('score', 0):.2f})
• Urgency: {result.urgency_score:.2f}
• Intent: {result.intent.get('label', 'unknown')}
• HVAC Concepts: {', '.join(result.hvac_concepts)}
• Processing Time: {result.processing_time:.3f}s
            """
            
            # Show results in a dialog
            self.show_dialog("Email Analysis Results", analysis_text)
            
        except Exception as ex:
            self.show_dialog("Error", f"Analysis failed: {str(ex)}")
    
    async def test_conversational_agent(self, e):
        """Test conversational agent"""
        try:
            task_id = await self.agent_manager.submit_task(
                agent_type=AgentType.CONVERSATIONAL,
                description="Generate email response",
                input_data={
                    "task_type": "email_response",
                    "urgency": "high",
                    "sentiment": "negative"
                },
                priority=TaskPriority.HIGH
            )
            
            result = await self.agent_manager.execute_next_task()
            
            if result and result.get("success"):
                response = result.get("response_text", "No response generated")
                self.show_dialog("Agent Response", f"Generated response:\n\n{response}")
            else:
                self.show_dialog("Error", "Agent task failed")
                
        except Exception as ex:
            self.show_dialog("Error", f"Agent test failed: {str(ex)}")
    
    def check_emails(self, e):
        """Check for new emails"""
        self.show_dialog("Email Check", "Email monitoring not yet implemented")
    
    def search_customers(self, e):
        """Search customers"""
        query = e.control.value
        self.show_dialog("Search", f"Searching for: {query}")
    
    def show_agent_status(self, e):
        """Show agent status"""
        status = self.agent_manager.get_queue_status()
        status_text = f"""
Agent Queue Status:
• Queue Length: {status['queue_length']}
• Active Tasks: {status['active_tasks']}
• Completed Tasks: {status['completed_tasks']}
        """
        self.show_dialog("Agent Status", status_text)
    
    def show_dialog(self, title: str, content: str):
        """Show dialog with content"""
        dialog = ft.AlertDialog(
            title=ft.Text(title),
            content=ft.Text(content),
            actions=[ft.TextButton("OK", on_click=lambda e: self.close_dialog(dialog))]
        )
        self.page.dialog = dialog
        dialog.open = True
        self.page.update()
    
    def close_dialog(self, dialog):
        """Close dialog"""
        dialog.open = False
        self.page.update()

async def main(page: ft.Page):
    """Main application entry point"""
    app = SemanticHVACApp(page)
    app.build_app()

if __name__ == "__main__":
    ft.app(target=main)