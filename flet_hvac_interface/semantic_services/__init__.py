"""
🧠 Semantic Services Layer for HVAC CRM Flet Interface
Modular semantic framework integration
"""

# Framework components
from .framework.manager import SemanticFrameworkManager
from .framework.analyzer import SemanticAnalyzer
from .framework.config import FrameworkConfig, SemanticAnalysisResult

# Agent components
from .agents.manager import HVACAgentManager
from .agents.types import AgentType, TaskPriority
from .agents.conversational import ConversationalAgent

# Simplified imports for main services
from .customer_intelligence import CustomerIntelligenceService
from .email_processor import EmailProcessorService
from .data_pipeline import DataPipelineOrchestrator

__all__ = [
    # Framework
    'SemanticFrameworkManager',
    'SemanticAnalyzer',
    'FrameworkConfig',
    'SemanticAnalysisResult',
    
    # Agents
    'HVACAgentManager',
    'AgentType',
    'TaskPriority',
    'ConversationalAgent',
    
    # Main services
    'CustomerIntelligenceService',
    'EmailProcessorService',
    'DataPipelineOrchestrator'
]