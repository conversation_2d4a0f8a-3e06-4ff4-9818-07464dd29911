"""
🧠 Semantic Framework Manager
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .config import FrameworkConfig, SemanticAnalysisResult
from .analyzer import SemanticAnalyzer

logger = logging.getLogger(__name__)

class SemanticFrameworkManager:
    """
    🧠 Main Semantic Framework Manager
    Simplified interface for semantic analysis
    """
    
    def __init__(self):
        self.config = FrameworkConfig()
        self.analyzer = SemanticAnalyzer()
        self.is_initialized = True
        self.analysis_cache = {}
        
        self.performance_metrics = {
            "total_analyses": 0,
            "successful_analyses": 0,
            "average_response_time": 0.0,
            "cache_hit_rate": 0.0
        }
        
        logger.info("🧠 Semantic Framework Manager initialized")
    
    async def analyze_customer_communication(self, 
                                           communication_text: str,
                                           customer_id: Optional[str] = None,
                                           communication_type: str = "email") -> SemanticAnalysisResult:
        """Analyze customer communication"""
        try:
            # Check cache
            cache_key = f"{hash(communication_text)}_{communication_type}"
            if cache_key in self.analysis_cache:
                self._update_metrics(True, 0.001, True)
                return self.analysis_cache[cache_key]
            
            # Perform analysis
            result = await self.analyzer.analyze_text(communication_text)
            
            # Cache result
            self.analysis_cache[cache_key] = result
            
            # Update metrics
            self._update_metrics(True, result.processing_time, False)
            
            logger.info(f"✅ Communication analysis completed in {result.processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"❌ Communication analysis failed: {e}")
            self._update_metrics(False, 0, False)
            return self._create_mock_result(str(e))
    
    def get_framework_status(self) -> Dict[str, Any]:
        """Get framework status"""
        return {
            "initialized": self.is_initialized,
            "cache_size": len(self.analysis_cache),
            "performance_metrics": self.performance_metrics,
            "timestamp": datetime.now().isoformat()
        }
    
    def _update_metrics(self, success: bool, processing_time: float, cache_hit: bool):
        """Update performance metrics"""
        self.performance_metrics["total_analyses"] += 1
        
        if success:
            self.performance_metrics["successful_analyses"] += 1
        
        # Update average response time
        total = self.performance_metrics["total_analyses"]
        current_avg = self.performance_metrics["average_response_time"]
        self.performance_metrics["average_response_time"] = (
            (current_avg * (total - 1) + processing_time) / total
        )
        
        # Update cache hit rate
        if cache_hit:
            cache_hits = self.performance_metrics.get("cache_hits", 0) + 1
            self.performance_metrics["cache_hits"] = cache_hits
            self.performance_metrics["cache_hit_rate"] = cache_hits / total
    
    def _create_mock_result(self, error: str) -> SemanticAnalysisResult:
        """Create mock result for errors"""
        return SemanticAnalysisResult(
            success=False,
            analysis_id=f"mock_{datetime.now().timestamp()}",
            sentiment={"label": "neutral", "score": 0.5},
            intent={"label": "service_request", "confidence": 0.7},
            urgency_score=0.4,
            keywords=["hvac", "service"],
            hvac_concepts=["air_conditioning"],
            customer_insights={"error": error},
            processing_time=0.1,
            timestamp=datetime.now()
        )
    
    def clear_cache(self):
        """Clear analysis cache"""
        self.analysis_cache.clear()
        logger.info("🧹 Analysis cache cleared")