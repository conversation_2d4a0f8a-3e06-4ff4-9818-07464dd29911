"""
😊 Sentiment Analysis Module
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class SentimentAnalyzer:
    """Analyzes sentiment in text"""
    
    def __init__(self):
        self.negative_words = [
            "problem", "issue", "broken", "not working", "complaint",
            "problem", "awaria", "nie działa", "zepsuty", "źle"
        ]
        
        self.positive_words = [
            "thank", "great", "excellent", "satisfied", "good", 
            "working well", "dziękuję", "świetnie", "dobrze", "super"
        ]
    
    def analyze(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment in text"""
        text_lower = text.lower()
        
        negative_count = sum(1 for word in self.negative_words if word in text_lower)
        positive_count = sum(1 for word in self.positive_words if word in text_lower)
        
        if negative_count > positive_count:
            return {
                "label": "negative", 
                "score": min(0.3 + negative_count * 0.2, 0.9),
                "confidence": 0.8
            }
        elif positive_count > negative_count:
            return {
                "label": "positive", 
                "score": min(0.6 + positive_count * 0.1, 0.9),
                "confidence": 0.8
            }
        else:
            return {
                "label": "neutral", 
                "score": 0.5,
                "confidence": 0.6
            }