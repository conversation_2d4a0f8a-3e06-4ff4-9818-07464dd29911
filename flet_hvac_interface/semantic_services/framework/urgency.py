"""
🚨 Urgency Detection Module
"""

import re
import logging

logger = logging.getLogger(__name__)

class UrgencyDetector:
    """Detects urgency level in text"""
    
    def __init__(self):
        self.urgent_words = [
            "urgent", "emergency", "asap", "immediately", "critical", 
            "broken", "pilne", "awaria", "natychmiast", "krytyczne"
        ]
        
        self.urgent_patterns = [
            r"nie działa", r"zepsuty", r"awaria", r"pilne",
            r"not working", r"broken", r"emergency", r"urgent"
        ]
    
    def detect(self, text: str) -> float:
        """Detect urgency level (0.0 to 1.0)"""
        text_lower = text.lower()
        
        # Count urgent words
        word_count = sum(1 for word in self.urgent_words if word in text_lower)
        
        # Count urgent patterns
        pattern_count = sum(1 for pattern in self.urgent_patterns 
                          if re.search(pattern, text_lower))
        
        # Calculate urgency score
        urgency_score = min((word_count * 0.3) + (pattern_count * 0.2), 1.0)
        
        return urgency_score
    
    def classify_urgency(self, urgency_score: float) -> str:
        """Classify urgency level"""
        if urgency_score >= 0.9:
            return "critical"
        elif urgency_score >= 0.7:
            return "high"
        elif urgency_score >= 0.4:
            return "medium"
        else:
            return "low"