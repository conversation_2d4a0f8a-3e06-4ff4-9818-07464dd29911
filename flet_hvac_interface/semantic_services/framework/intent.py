"""
🎯 Intent Classification Module
"""

import re
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class IntentClassifier:
    """Classifies user intent from text"""
    
    def __init__(self):
        self.intent_patterns = {
            "service_request": [
                r"serwis", r"naprawa", r"repair", r"fix", r"problem", r"issue"
            ],
            "quote_request": [
                r"wycena", r"cena", r"koszt", r"quote", r"price", r"cost"
            ],
            "scheduling": [
                r"termin", r"wizyta", r"umówić", r"schedule", r"appointment"
            ],
            "maintenance": [
                r"przegląd", r"konserwacja", r"maintenance", r"service"
            ],
            "installation": [
                r"montaż", r"instalacja", r"installation", r"install"
            ],
            "complaint": [
                r"skarga", r"reklamacja", r"complaint", r"dissatisfied"
            ]
        }
    
    def classify(self, text: str) -> Dict[str, Any]:
        """Classify intent from text"""
        text_lower = text.lower()
        
        best_intent = "general_inquiry"
        best_confidence = 0.3
        
        for intent, patterns in self.intent_patterns.items():
            matches = sum(1 for pattern in patterns 
                         if re.search(pattern, text_lower))
            confidence = min(matches * 0.3, 0.9)
            
            if confidence > best_confidence:
                best_intent = intent
                best_confidence = confidence
        
        return {
            "label": best_intent, 
            "confidence": best_confidence,
            "category": self._get_intent_category(best_intent)
        }
    
    def _get_intent_category(self, intent: str) -> str:
        """Get category for intent"""
        categories = {
            "service_request": "service",
            "maintenance": "service", 
            "installation": "sales",
            "quote_request": "sales",
            "scheduling": "operations",
            "complaint": "support",
            "general_inquiry": "general"
        }
        return categories.get(intent, "general")