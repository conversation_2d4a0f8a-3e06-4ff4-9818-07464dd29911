"""
🔧 HVAC Concepts Extractor
"""

import logging
from typing import List

logger = logging.getLogger(__name__)

class HVACConceptExtractor:
    """Extracts HVAC-related concepts from text"""
    
    def __init__(self):
        self.hvac_terms = [
            "air conditioning", "heating", "hvac", "compressor", 
            "filter", "maintenance", "repair", "installation",
            "klimatyzacja", "ogrzewanie", "serwis", "naprawa",
            "montaż", "filtr", "sprężarka", "wentylacja"
        ]
        
        self.equipment_types = [
            "split", "multi split", "vrf", "vrv", "cassette",
            "duct", "window", "portable", "central"
        ]
        
        self.brands = [
            "lg", "daikin", "mitsubishi", "carrier", "trane",
            "fujitsu", "panasonic", "samsung", "gree"
        ]
    
    def extract(self, text: str) -> List[str]:
        """Extract HVAC concepts from text"""
        text_lower = text.lower()
        concepts = []
        
        # Extract general HVAC terms
        for term in self.hvac_terms:
            if term in text_lower:
                concepts.append(term.replace(" ", "_"))
        
        # Extract equipment types
        for equipment in self.equipment_types:
            if equipment in text_lower:
                concepts.append(f"equipment:{equipment.replace(' ', '_')}")
        
        # Extract brands
        for brand in self.brands:
            if brand in text_lower:
                concepts.append(f"brand:{brand}")
        
        # Add issue indicators
        issue_words = ["problem", "broken", "not working", "awaria", "zepsuty"]
        if any(word in text_lower for word in issue_words):
            concepts.append("issue:system_problem")
        
        return list(set(concepts))