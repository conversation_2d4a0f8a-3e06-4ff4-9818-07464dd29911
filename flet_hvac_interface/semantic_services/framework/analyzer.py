"""
🔍 Main Semantic Analyzer
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import re

from .config import SemanticAnalysisResult
from .sentiment import SentimentAnalyzer
from .urgency import UrgencyDetector
from .intent import IntentClassifier
from .hvac_concepts import HVACConceptExtractor

logger = logging.getLogger(__name__)

class SemanticAnalyzer:
    """
    🔍 Main Semantic Analysis Engine
    Coordinates all analysis components
    """
    
    def __init__(self):
        self.sentiment_analyzer = SentimentAnalyzer()
        self.urgency_detector = UrgencyDetector()
        self.intent_classifier = IntentClassifier()
        self.hvac_extractor = HVACConceptExtractor()
        
        logger.info("🔍 Semantic Analyzer initialized")
    
    async def analyze_text(self, text: str) -> SemanticAnalysisResult:
        """Perform comprehensive semantic analysis"""
        start_time = datetime.now()
        
        try:
            # Run all analyses
            sentiment = self.sentiment_analyzer.analyze(text)
            urgency_score = self.urgency_detector.detect(text)
            intent = self.intent_classifier.classify(text)
            hvac_concepts = self.hvac_extractor.extract(text)
            keywords = self._extract_keywords(text)
            
            # Generate insights
            customer_insights = self._generate_insights(
                sentiment, urgency_score, intent, hvac_concepts
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return SemanticAnalysisResult(
                success=True,
                analysis_id=f"analysis_{datetime.now().timestamp()}",
                sentiment=sentiment,
                intent=intent,
                urgency_score=urgency_score,
                keywords=keywords,
                hvac_concepts=hvac_concepts,
                customer_insights=customer_insights,
                processing_time=processing_time,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"❌ Semantic analysis failed: {e}")
            return self._create_error_result(e, start_time)
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract relevant keywords"""
        words = re.findall(r'\b\w+\b', text.lower())
        
        stop_words = {
            "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", 
            "with", "by", "a", "an", "is", "are", "was", "were", "be", "been"
        }
        
        keywords = [word for word in words if len(word) > 3 and word not in stop_words]
        return list(set(keywords))[:10]
    
    def _generate_insights(self, sentiment: Dict[str, Any], urgency_score: float, 
                          intent: Dict[str, Any], hvac_concepts: List[str]) -> Dict[str, Any]:
        """Generate customer insights"""
        return {
            "satisfaction_level": self._get_satisfaction_level(sentiment),
            "service_urgency": self.urgency_detector.classify_urgency(urgency_score),
            "equipment_focus": [c for c in hvac_concepts if not c.startswith("issue:")],
            "recommended_actions": self._get_recommended_actions(sentiment, urgency_score, hvac_concepts)
        }
    
    def _get_satisfaction_level(self, sentiment: Dict[str, Any]) -> str:
        """Get satisfaction level from sentiment"""
        label = sentiment.get("label", "neutral")
        if label == "positive":
            return "high"
        elif label == "negative":
            return "low"
        else:
            return "medium"
    
    def _get_recommended_actions(self, sentiment: Dict[str, Any], urgency_score: float, 
                                hvac_concepts: List[str]) -> List[str]:
        """Get recommended actions"""
        actions = []
        
        if urgency_score > 0.7:
            actions.append("Schedule immediate technician visit")
        if sentiment.get("label") == "negative":
            actions.append("Follow up with customer satisfaction survey")
        if any("maintenance" in c for c in hvac_concepts):
            actions.append("Review maintenance schedule")
        
        return actions or ["Monitor customer communication"]
    
    def _create_error_result(self, error: Exception, start_time: datetime) -> SemanticAnalysisResult:
        """Create error result"""
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return SemanticAnalysisResult(
            success=False,
            analysis_id=f"failed_{datetime.now().timestamp()}",
            sentiment={"label": "neutral", "score": 0.5},
            intent={"label": "unknown", "confidence": 0.0},
            urgency_score=0.0,
            keywords=[],
            hvac_concepts=[],
            customer_insights={"error": str(error)},
            processing_time=processing_time,
            timestamp=datetime.now()
        )