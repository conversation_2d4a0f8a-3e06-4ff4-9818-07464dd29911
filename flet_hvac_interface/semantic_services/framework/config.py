"""
⚙️ Framework Configuration
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass

@dataclass
class FrameworkConfig:
    """Main framework configuration"""
    # Weaviate configuration
    weaviate_url: str = "http://localhost:8080"
    weaviate_api_key: Optional[str] = None
    
    # Semantic analysis configuration
    semantic_models: Dict[str, str] = None
    
    # Agent configuration
    max_concurrent_agents: int = 10
    agent_timeout: int = 300
    
    # Integration configuration
    enable_real_time: bool = True
    enable_monitoring: bool = True
    enable_persistence: bool = True
    
    # HVAC specific configuration
    hvac_equipment_brands: List[str] = None
    hvac_service_types: List[str] = None
    
    def __post_init__(self):
        if self.semantic_models is None:
            self.semantic_models = {
                "sentence_transformer": "all-MiniLM-L6-v2",
                "spacy_model": "en_core_web_sm",
                "sentiment_model": "cardiffnlp/twitter-roberta-base-sentiment-latest"
            }
        
        if self.hvac_equipment_brands is None:
            self.hvac_equipment_brands = ["LG", "Daikin", "Mitsubishi", "Carrier", "Trane", "Fujitsu"]
        
        if self.hvac_service_types is None:
            self.hvac_service_types = ["installation", "maintenance", "repair", "inspection", "emergency"]

@dataclass
class SemanticAnalysisResult:
    """Result of semantic analysis"""
    success: bool
    analysis_id: str
    sentiment: Dict[str, Any]
    intent: Dict[str, Any]
    urgency_score: float
    keywords: List[str]
    hvac_concepts: List[str]
    customer_insights: Dict[str, Any]
    processing_time: float
    timestamp: datetime