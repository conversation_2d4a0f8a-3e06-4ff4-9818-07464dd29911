"""
🗣️ Conversational Agent
"""

import logging
from typing import Dict, Any
from datetime import datetime

from .types import AgentType, TaskStatus
from .task import AgentTask
from .base import HVACAgent

logger = logging.getLogger(__name__)

class ConversationalAgent(HVACAgent):
    """🗣️ Conversational Agent - Customer service automation"""
    
    def __init__(self):
        super().__init__(AgentType.CONVERSATIONAL, "Customer Service Assistant")
        self.capabilities = [
            "Email response generation",
            "Customer sentiment analysis",
            "Automated ticket creation",
            "FAQ responses",
            "Appointment scheduling assistance"
        ]
    
    async def execute_task(self, task: AgentTask) -> Dict[str, Any]:
        """Execute conversational task"""
        try:
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            
            task_type = task.input_data.get("task_type", "general")
            
            if task_type == "email_response":
                result = await self._generate_email_response(task.input_data)
            elif task_type == "sentiment_analysis":
                result = await self._analyze_sentiment(task.input_data)
            elif task_type == "ticket_creation":
                result = await self._create_ticket(task.input_data)
            else:
                result = await self._handle_general_conversation(task.input_data)
            
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.result = result
            
            self._update_metrics(task, True)
            return result
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = str(e)
            task.completed_at = datetime.now()
            self._update_metrics(task, False)
            logger.error(f"❌ Conversational agent task failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def _generate_email_response(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate automated email response"""
        urgency = data.get("urgency", "medium")
        sentiment = data.get("sentiment", "neutral")
        
        if urgency == "high":
            response = "Thank you for contacting us. We understand this is urgent and will prioritize your request."
        elif sentiment == "negative":
            response = "We apologize for any inconvenience. Let us resolve this issue for you immediately."
        else:
            response = "Thank you for your message. We'll get back to you within 24 hours."
        
        return {
            "success": True,
            "response_text": response,
            "response_type": "automated",
            "requires_human_review": urgency == "critical"
        }