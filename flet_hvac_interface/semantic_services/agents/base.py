"""
🤖 Base Agent Class
"""

import logging
from typing import Dict, Optional, Any
from datetime import datetime

from .types import AgentType
from .task import AgentTask, AgentMetrics

logger = logging.getLogger(__name__)

class HVACAgent:
    """Base class for HVAC agents"""
    
    def __init__(self, agent_type: AgentType, name: str):
        self.agent_type = agent_type
        self.name = name
        self.is_active = False
        self.current_task: Optional[AgentTask] = None
        self.metrics = AgentMetrics()
        self.capabilities = []
        logger.info(f"🤖 {name} agent initialized")
    
    async def execute_task(self, task: AgentTask) -> Dict[str, Any]:
        """Execute a task (to be implemented by subclasses)"""
        raise NotImplementedError("Subclasses must implement execute_task")
    
    def get_status(self) -> Dict[str, Any]:
        """Get agent status"""
        return {
            "agent_type": self.agent_type.value,
            "name": self.name,
            "is_active": self.is_active,
            "current_task": self.current_task.task_id if self.current_task else None,
            "metrics": self._get_metrics_dict(),
            "capabilities": self.capabilities
        }
    
    def _get_metrics_dict(self) -> Dict[str, Any]:
        """Get metrics as dictionary"""
        return {
            "total_tasks": self.metrics.total_tasks,
            "completed_tasks": self.metrics.completed_tasks,
            "failed_tasks": self.metrics.failed_tasks,
            "success_rate": self.metrics.success_rate,
            "average_execution_time": self.metrics.average_execution_time,
            "last_activity": self.metrics.last_activity.isoformat() if self.metrics.last_activity else None
        }
    
    def _update_metrics(self, task: AgentTask, success: bool):
        """Update agent metrics"""
        self.metrics.total_tasks += 1
        self.metrics.last_activity = datetime.now()
        
        if success:
            self.metrics.completed_tasks += 1
        else:
            self.metrics.failed_tasks += 1
        
        self.metrics.success_rate = self.metrics.completed_tasks / self.metrics.total_tasks
        
        if task.started_at and task.completed_at:
            execution_time = (task.completed_at - task.started_at).total_seconds()
            self._update_execution_time(execution_time)
    
    def _update_execution_time(self, execution_time: float):
        """Update average execution time"""
        total_completed = self.metrics.completed_tasks
        current_avg = self.metrics.average_execution_time
        self.metrics.average_execution_time = (
            (current_avg * (total_completed - 1) + execution_time) / total_completed
        )