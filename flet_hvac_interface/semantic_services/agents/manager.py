"""
🎯 HVAC Agent Manager
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .types import AgentType, TaskPriority
from .task import AgentTask
from .conversational import ConversationalAgent

logger = logging.getLogger(__name__)

class HVACAgentManager:
    """
    🎯 HVAC Agent Manager
    Simplified agent orchestration
    """
    
    def __init__(self):
        self.agents: Dict[AgentType, Any] = {
            AgentType.CONVERSATIONAL: ConversationalAgent()
        }
        
        self.task_queue: List[AgentTask] = []
        self.active_tasks: Dict[str, AgentTask] = {}
        self.completed_tasks: List[AgentTask] = []
        self.is_running = False
        
        logger.info("🎯 HVAC Agent Manager initialized")
    
    async def submit_task(self, 
                         agent_type: AgentType,
                         description: str,
                         input_data: Dict[str, Any],
                         priority: TaskPriority = TaskPriority.MEDIUM) -> str:
        """Submit a task to the appropriate agent"""
        task_id = f"{agent_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        
        task = AgentTask(
            task_id=task_id,
            agent_type=agent_type,
            priority=priority,
            description=description,
            input_data=input_data
        )
        
        self.task_queue.append(task)
        self.task_queue.sort(key=lambda t: t.priority.value, reverse=True)
        
        logger.info(f"📋 Task {task_id} submitted to {agent_type.value} agent")
        return task_id
    
    async def execute_next_task(self) -> Optional[Dict[str, Any]]:
        """Execute the next task in the queue"""
        if not self.task_queue:
            return None
        
        task = self.task_queue.pop(0)
        self.active_tasks[task.task_id] = task
        
        agent = self.agents.get(task.agent_type)
        if not agent:
            logger.error(f"❌ No agent for type: {task.agent_type}")
            return {"success": False, "error": "Agent not found"}
        
        try:
            result = await agent.execute_task(task)
            
            # Move to completed tasks
            self.completed_tasks.append(task)
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
            
            logger.info(f"✅ Task {task.task_id} completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"❌ Task {task.task_id} execution failed: {e}")
            return {"success": False, "error": str(e), "task_id": task.task_id}
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get task queue status"""
        return {
            "queue_length": len(self.task_queue),
            "active_tasks": len(self.active_tasks),
            "completed_tasks": len(self.completed_tasks)
        }