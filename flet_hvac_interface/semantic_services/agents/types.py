"""
🏷️ Agent Types and Enums
"""

from enum import Enum

class AgentType(Enum):
    """5 specialized HVAC agent types"""
    CONVERSATIONAL = "conversational"
    ANALYTICAL = "analytical"
    DECISION_MAKING = "decision_making"
    INTEGRATION = "integration"
    OPTIMIZATION = "optimization"

class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4