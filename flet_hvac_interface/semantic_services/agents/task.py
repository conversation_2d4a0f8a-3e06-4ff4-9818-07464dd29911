"""
📋 Agent Task Classes
"""

from typing import Dict, Optional, Any
from datetime import datetime
from dataclasses import dataclass, field

from .types import AgentType, TaskStatus, TaskPriority

@dataclass
class AgentTask:
    """Individual agent task"""
    task_id: str
    agent_type: AgentType
    priority: TaskPriority
    description: str
    input_data: Dict[str, Any]
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    progress: float = 0.0

@dataclass
class AgentMetrics:
    """Agent performance metrics"""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    average_execution_time: float = 0.0
    success_rate: float = 0.0
    last_activity: Optional[datetime] = None